import type { <PERSON>ada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: 'swap',
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
  display: 'swap',
});

export const metadata: Metadata = {
  title: "Nischal Gautam - Full Stack Developer",
  description: "Portfolio of Nischal Gautam - Full Stack Developer specializing in React, Node.js, and modern web technologies",
  keywords: "Full Stack Developer, React, Node.js, JavaScript, Portfolio, Web Development",
  authors: [{ name: "<PERSON><PERSON><PERSON> Gautam" }],
  openGraph: {
    title: "Nischal Gautam - Full Stack Developer",
    description: "Portfolio of Nischal Gautam - Full Stack Developer specializing in React, Node.js, and modern web technologies",
    type: "website",
    url: "https://nischalgautam.com.np",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} antialiased px-4 md:px-8 lg:px-16`}
      >
        {children}
      </body>
    </html>
  );
}
