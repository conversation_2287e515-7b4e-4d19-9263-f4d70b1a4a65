@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  --background: #0f172a;
  --foreground: #f8fafc;
  --primary: #3b82f6;
  --secondary: #8b5cf6;
  --accent: #06b6d4;
  --surface: #1e293b;
  --text-secondary: #94a3b8;
  --success: #10b981;
  --glass-bg: rgba(30, 41, 59, 0.3);
  --glass-border: rgba(148, 163, 184, 0.1);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-surface: var(--surface);
  --color-text-secondary: var(--text-secondary);
  --color-success: var(--success);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', monospace;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary);
}

/* Glassmorphism utility classes */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

.glass-strong {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, var(--primary), var(--secondary), var(--accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Particle animation */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.particle {
  animation: float 6s ease-in-out infinite;
}

/* Glow effects */
.glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.glow-hover:hover {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  transition: box-shadow 0.3s ease;
}

/* Button styles */
.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.btn-outline {
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
  padding: 10px 22px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-outline:hover {
  background: var(--primary);
  color: white;
  transform: translateY(-2px);
}

/* Modern Input Styles */
.modern-input {
  background: rgba(30, 41, 59, 0.4);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  padding: 16px 20px;
  color: white;
  font-size: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.modern-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(30, 41, 59, 0.6);
}

.modern-input::placeholder {
  color: rgba(148, 163, 184, 0.7);
}

.modern-label {
  color: rgba(248, 250, 252, 0.9);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

/* Section spacing */
.section-spacing {
  padding: 160px 0;
  margin-bottom: 40px;
}

@media (max-width: 1024px) {
  .section-spacing {
    padding: 120px 0;
    margin-bottom: 30px;
  }
}

@media (max-width: 768px) {
  .section-spacing {
    padding: 100px 0;
    margin-bottom: 20px;
  }
}

/* Hero section specific spacing */
.hero-section {
  padding-bottom: 80px;
  margin-bottom: 60px;
}

@media (max-width: 768px) {
  .hero-section {
    padding-bottom: 60px;
    margin-bottom: 40px;
  }
}

/* Footer spacing */
.footer-spacing {
  margin-top: 80px;
  padding-top: 60px;
}

@media (max-width: 768px) {
  .footer-spacing {
    margin-top: 60px;
    padding-top: 40px;
  }
}

/* Section dividers */
.section-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(148, 163, 184, 0.2), transparent);
  margin: 80px 0;
}

@media (max-width: 768px) {
  .section-divider {
    margin: 60px 0;
  }
}

/* Technology constellation styles */
.tech-constellation {
  position: relative;
  min-height: 400px;
}

.tech-bubble {
  position: absolute;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.tech-bubble:hover {
  transform: scale(1.1);
  z-index: 10;
}

.tech-category-card {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.9));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 24px;
  overflow: hidden;
  position: relative;
}

.tech-category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tech-category-card:hover::before {
  opacity: 1;
}

.floating-tech {
  animation: float 6s ease-in-out infinite;
}

.floating-tech:nth-child(2n) {
  animation-delay: -2s;
}

.floating-tech:nth-child(3n) {
  animation-delay: -4s;
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg); 
  }
  33% { 
    transform: translateY(-15px) rotate(1deg); 
  }
  66% { 
    transform: translateY(-5px) rotate(-1deg); 
  }
}

.skill-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.skill-tag {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: default;
  position: relative;
  overflow: hidden;
}

.skill-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.skill-tag:hover::before {
  left: 100%;
}

/* Consistent card padding */
.card-padding {
  padding-left: 32px;
  padding-right: 32px;
}

.card-padding-lg {
  padding-left: 40px;
  padding-right: 40px;
}

.card-padding-sm {
  padding-left: 24px;
  padding-right: 24px;
}

@media (max-width: 768px) {
  .card-padding {
    padding-left: 24px;
    padding-right: 24px;
  }
  
  .card-padding-lg {
    padding-left: 28px;
    padding-right: 28px;
  }
  
  .card-padding-sm {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media (max-width: 480px) {
  .card-padding {
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .card-padding-lg {
    padding-left: 24px;
    padding-right: 24px;
  }
  
  .card-padding-sm {
    padding-left: 16px;
    padding-right: 16px;
  }
}

/* Vertical card padding */
.card-padding-y {
  padding-top: 24px;
  padding-bottom: 24px;
}

.card-padding-y-lg {
  padding-top: 32px;
  padding-bottom: 32px;
}

.card-padding-y-sm {
  padding-top: 16px;
  padding-bottom: 16px;
}

@media (max-width: 768px) {
  .card-padding-y {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  
  .card-padding-y-lg {
    padding-top: 24px;
    padding-bottom: 24px;
  }
  
  .card-padding-y-sm {
    padding-top: 12px;
    padding-bottom: 12px;
  }
}

/* Navigation spacing */
.nav-spacing {
  gap: 32px;
}

@media (max-width: 1024px) {
  .nav-spacing {
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .nav-spacing {
    gap: 16px;
  }
}

/* Subsection spacing */
.subsection-spacing {
  margin-top: 60px;
}

.subsection-spacing-lg {
  margin-top: 80px;
}

@media (max-width: 768px) {
  .subsection-spacing {
    margin-top: 40px;
  }
  
  .subsection-spacing-lg {
    margin-top: 50px;
  }
}

/* Call to action spacing */
.cta-spacing {
  margin-top: 80px;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .cta-spacing {
    margin-top: 60px;
    padding-top: 30px;
  }
}

/* Enhanced text spacing */
.text-spacing {
  line-height: 1.8;
  margin-bottom: 24px;
}

.text-spacing-lg {
  line-height: 1.9;
  margin-bottom: 32px;
}

/* Content blocks */
.content-block {
  padding: 32px;
  margin-bottom: 24px;
}

.content-block-lg {
  padding: 40px;
  margin-bottom: 32px;
}

@media (max-width: 768px) {
  .content-block {
    padding: 24px;
    margin-bottom: 20px;
  }
  
  .content-block-lg {
    padding: 28px;
    margin-bottom: 24px;
  }
}

/* Improved card styling */
.modern-card {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.15);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.modern-card:hover {
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.15);
}

.modern-card-subtle {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
}