'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';
// import WorkIcon from '@mui/icons-material/Work';
import LaunchIcon from '@mui/icons-material/Launch';

const Experience = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const experiences = [
    {
      title: "Full Stack Developer",
      company: "Tradeawaay",
      period: "June 2025 – Present",
      type: "Full-time",
      achievements: [
        "Built a trading platform with real-time charts, chat features, and user dashboard",
        "Implemented copy trading, social trading features, and developed the company landing page",
        "Optimized trade copying time from chat by 50% (4 seconds to 2 seconds)",
        "Developed frontend using Vue.js and backend using Node.js/Express in microservices architecture"
      ],
      technologies: ["Vue.js", "Node.js", "Express", "Microservices", "Real-time", "WebSocket"],
      color: "from-blue-500 to-cyan-500"
    },
    {
      title: "Frontend Developer Intern",
      company: "SRIYOG",
      period: "April 2025 – May 2025",
      type: "Internship",
      certificate: "https://drive.google.com/file/d/1C1toi3Z5kn7oxqhlHh2TU0nJIdjNj82a/view?usp=sharing",
      achievements: [
        "Cloned pusom.edu.np website using React.js and Tailwind CSS",
        "Implemented new responsive designs for homepage, news, downloads, and gallery sections",
        "Collaborated with developers and designers to ensure user-friendly interfaces"
      ],
      technologies: ["React.js", "Tailwind CSS", "Responsive Design", "UI/UX"],
      color: "from-green-500 to-emerald-500"
    },
    {
      title: "Full Stack Developer Intern",
      company: "Codynn",
      period: "May 2025",
      type: "Remote Internship",
      achievements: [
        "Designed and developed dashboard for entrance preparation platform",
        "Integrated API endpoints with frontend and wrote backend API endpoints"
      ],
      technologies: ["React.js", "Node.js", "API Integration", "Dashboard"],
      color: "from-purple-500 to-pink-500"
    }
  ];

  return (
    <section id="experience" className="section-spacing relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-20 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="space-y-20"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center space-y-4">
            <h2 className="text-4xl md:text-5xl font-bold">
              Work <span className="gradient-text">Experience</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Building innovative solutions and gaining valuable experience across different domains
            </p>
          </motion.div>

          {/* Timeline */}
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-500 via-purple-500 to-pink-500 rounded-full" />

            <div className="space-y-16">
              {experiences.map((exp, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className={`relative flex items-center ${
                    index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                  }`}
                >
                  {/* Timeline Node */}
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={isInView ? { scale: 1 } : { scale: 0 }}
                    transition={{ delay: index * 0.2 + 0.5 }}
                    className="absolute left-8 md:left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white rounded-full border-4 border-blue-500 z-10"
                  />

                  {/* Content Card */}
                  <motion.div
                    whileHover={{ scale: 1.02, y: -5 }}
                    className={`w-full md:w-5/12 ml-16 md:ml-0 ${
                      index % 2 === 0 ? 'md:mr-auto md:pr-8' : 'md:ml-auto md:pl-8'
                    }`}
                  >
                    <div className="glass-strong rounded-2xl py-5 px-5 hover:border-blue-500/30 transition-all duration-300">
                      {/* Header */}
                      <div className="flex items-start justify-between mb-4">
                        <div className="space-y-1">
                          <h3 className="text-xl font-bold text-white">{exp.title}</h3>
                          <div className="flex items-center gap-2">
                            <LaunchIcon className="text-accent text-sm" />
                            <span className="text-lg font-semibold gradient-text">{exp.company}</span>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-gray-400">
                            <span>{exp.period}</span>
                            <span className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">
                              {exp.type}
                            </span>
                          </div>
                        </div>
                        
                        {exp.certificate && (
                          <motion.a
                            href={exp.certificate}
                            target="_blank"
                            rel="noopener noreferrer"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                            className="py-1.5 px-4 glass rounded-lg text-gray-400 hover:text-white hover:bg-blue-500/20 transition-all duration-300"
                            title="View Certificate"
                          >
                            <LaunchIcon fontSize="small" />
                          </motion.a>
                        )}
                      </div>

                      {/* Achievements */}
                      <div className="space-y-3 mb-6">
                        {exp.achievements.map((achievement, achIndex) => (
                          <motion.div
                            key={achIndex}
                            initial={{ opacity: 0, x: -20 }}
                            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
                            transition={{ delay: index * 0.2 + achIndex * 0.1 + 0.8 }}
                            className="flex items-start gap-3"
                          >
                            <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0" />
                            <p className="text-gray-300 leading-relaxed">{achievement}</p>
                          </motion.div>
                        ))}
                      </div>

                      {/* Technologies */}
                      <div className="space-y-3">
                        <h4 className="text-sm font-semibold text-gray-400 uppercase tracking-wide">
                          Technologies Used
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {exp.technologies.map((tech, techIndex) => (
                            <motion.span
                              key={tech}
                              initial={{ opacity: 0, scale: 0 }}
                              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0 }}
                              transition={{ delay: index * 0.2 + techIndex * 0.05 + 1 }}
                              className={` bg-gradient-to-r ${exp.color} bg-opacity-20 py-1 px-7 text-white rounded-full text-xs font-medium`}
                            >
                              {tech}
                            </motion.span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <motion.div variants={itemVariants} className="cta-spacing text-center space-y-8 ">
            <h3 className="text-2xl font-bold text-white">
              Ready to Work Together?
            </h3>
            <p className="text-gray-400 max-w-2xl mx-auto mt-5">
              I'm always open to discussing new opportunities and exciting projects. 
              Let's build something amazing together!
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary"
              onClick={() => document.querySelector('#contact')?.scrollIntoView({ behavior: 'smooth' })}
            >
              Get In Touch
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Experience;
