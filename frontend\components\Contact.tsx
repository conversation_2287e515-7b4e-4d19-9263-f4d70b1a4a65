'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef, useState } from 'react';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import GitHubIcon from '@mui/icons-material/GitHub';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import CodeIcon from '@mui/icons-material/Code';
import SendIcon from '@mui/icons-material/Send';

const Contact = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Create mailto link with form data
    const subject = encodeURIComponent(formData.subject || 'Contact from Portfolio');
    const body = encodeURIComponent(
      `Name: ${formData.name}\nEmail: ${formData.email}\n\nMessage:\n${formData.message}`
    );
    window.location.href = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
  };

  const contactInfo = [
    {
      icon: EmailIcon,
      label: "Email",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>",
      color: "from-red-500 to-pink-500"
    },
    {
      icon: PhoneIcon,
      label: "Phone",
      value: "+977-9821597160",
      href: "tel:+9779821597160",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: LocationOnIcon,
      label: "Location",
      value: "Kathmandu, Nepal",
      href: "#",
      color: "from-blue-500 to-cyan-500"
    }
  ];

  const socialLinks = [
    {
      icon: GitHubIcon,
      label: "GitHub",
      href: "https://github.com/NischalGautam8",
      color: "hover:bg-gray-700"
    },
    {
      icon: LinkedInIcon,
      label: "LinkedIn",
      href: "https://linkedin.com/in/nischalgautam8",
      color: "hover:bg-blue-600"
    },
    {
      icon: CodeIcon,
      label: "LeetCode",
      href: "https://leetcode.com/u/nischalgautam7200/",
      color: "hover:bg-orange-600"
    }
  ];

  return (
    <section id="contact" className="section-spacing relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl" />
        <div className="absolute top-60 right-40 w-64 h-64 bg-cyan-500/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="space-y-20"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center space-y-8">
            <h2 className="text-4xl md:text-6xl font-bold">
              Let's <span className="gradient-text">Connect</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-4xl mx-auto text-spacing-lg">
              Ready to bring your ideas to life? Let's discuss your next project and create something amazing together.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-5 gap-16">
            {/* Contact Information */}
            <motion.div variants={itemVariants} className="lg:col-span-2 space-y-12">
              {/* Introduction */}
              <div className="modern-card content-block-lg">
                <h3 className="text-3xl font-bold text-white mb-6">Get in Touch</h3>
                <p className="text-gray-300 text-spacing-lg">
                  I'm always excited to work on new projects and collaborate with amazing people.
                </p>
                <p className="text-gray-400 text-spacing">
                  Whether you have a project in mind, want to discuss opportunities, or just want to say hello, 
                  I'd love to hear from you!
                </p>
              </div>

              {/* Contact Info Cards */}
              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <motion.a
                    key={info.label}
                    href={info.href}
                    initial={{ opacity: 0, x: -50 }}
                    animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
                    transition={{ delay: index * 0.1 + 0.5 }}
                    whileHover={{ scale: 1.02, x: 8 }}
                    className="flex items-center gap-6 modern-card-subtle p-6 hover:border-blue-500/30 transition-all duration-300 group"
                  >
                    <div className={`p-4 rounded-xl bg-gradient-to-r ${info.color} bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300`}>
                      <info.icon className="text-white text-xl" />
                    </div>
                    <div className="space-y-1">
                      <h4 className="font-semibold text-white group-hover:text-blue-300 transition-colors text-lg">
                        {info.label}
                      </h4>
                      <p className="text-gray-400">{info.value}</p>
                    </div>
                  </motion.a>
                ))}
              </div>

              {/* Social Links */}
              <div className="modern-card-subtle content-block">
                <h4 className="text-xl font-bold text-white mb-6">Follow Me</h4>
                <div className="flex gap-4">
                  {socialLinks.map((social, index) => (
                    <motion.a
                      key={social.label}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      initial={{ opacity: 0, scale: 0 }}
                      animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0 }}
                      transition={{ delay: index * 0.1 + 0.8 }}
                      whileHover={{ scale: 1.1, y: -3 }}
                      whileTap={{ scale: 0.95 }}
                      className={`p-4 bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl text-gray-400 hover:text-white transition-all duration-300 border border-gray-700/50 hover:border-blue-500/50 ${social.color}`}
                      title={social.label}
                    >
                      <social.icon className="text-xl" />
                    </motion.a>
                  ))}
                </div>
              </div>

              {/* Availability Status */}
              <motion.div
                variants={itemVariants}
                className="modern-card content-block text-center"
              >
                <div className="space-y-4">
                  <div className="w-16 h-16 mx-auto bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                    <div className="w-3 h-3 bg-white rounded-full animate-pulse" />
                  </div>
                  <div>
                    <h5 className="text-green-400 font-bold text-lg mb-2">Available for Work</h5>
                    <p className="text-gray-400 text-sm leading-relaxed">
                      Currently open to new opportunities and exciting projects. 
                      Let's build something great together!
                    </p>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Contact Form */}
            <motion.div variants={itemVariants} className="lg:col-span-3">
              <div className="modern-card content-block-lg">
                <h3 className="text-3xl font-bold text-white mb-8 text-center">Send a Message</h3>
                
                <form onSubmit={handleSubmit} className="space-y-8">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="modern-label">
                        Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        required
                        value={formData.name}
                        onChange={handleInputChange}
                        className="w-full modern-input"
                        placeholder="Enter your full name"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="email" className="modern-label">
                        Email *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        required
                        value={formData.email}
                        onChange={handleInputChange}
                        className="w-full modern-input"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="subject" className="modern-label">
                      Subject
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      className="w-full modern-input"
                      placeholder="What would you like to discuss?"
                    />
                  </div>

                  <div>
                    <label htmlFor="message" className="modern-label">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      required
                      rows={6}
                      value={formData.message}
                      onChange={handleInputChange}
                      className="w-full modern-input resize-none"
                      placeholder="Tell me about your project, ideas, or just say hello! I'd love to hear from you."
                    />
                  </div>

                  <motion.button
                    type="submit"
                    whileHover={{ scale: 1.02, y: -2 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full btn-primary flex items-center justify-center gap-3 py-4 text-lg font-semibold"
                  >
                    <SendIcon />
                    Send Message
                  </motion.button>
                </form>

                {/* Quick Contact Options */}
                <div className="mt-12 pt-8 border-t border-gray-700/50">
                  <h4 className="text-lg font-semibold text-white mb-6 text-center">Or reach out directly</h4>
                  <div className="grid grid-cols-2 gap-6">
                    <motion.a
                      href="mailto:<EMAIL>"
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      className="modern-card-subtle p-6 text-center hover:border-blue-500/30 transition-all duration-300 group"
                    >
                      <EmailIcon className="text-blue-400 mb-3 mx-auto text-2xl" />
                      <p className="text-sm font-medium text-white group-hover:text-blue-300">
                        Quick Email
                      </p>
                    </motion.a>
                    
                    <motion.a
                      href="https://linkedin.com/in/nischalgautam8"
                      target="_blank"
                      rel="noopener noreferrer"
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.98 }}
                      className="modern-card-subtle p-6 text-center hover:border-blue-500/30 transition-all duration-300 group"
                    >
                      <LinkedInIcon className="text-blue-400 mb-3 mx-auto text-2xl" />
                      <p className="text-sm font-medium text-white group-hover:text-blue-300">
                        LinkedIn
                      </p>
                    </motion.a>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Contact;