'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef, useState } from 'react';
import LaunchIcon from '@mui/icons-material/Launch';
import GitHubIcon from '@mui/icons-material/GitHub';
import CodeIcon from '@mui/icons-material/Code';

const Projects = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [filter, setFilter] = useState('All');

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.9 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const projects = [
    {
      title: "UNIHUB",
      subtitle: "Social Media Platform for University Students",
      description: "A comprehensive social platform with real-time messaging, search, likes, comments, sharing, user profiles, note uploading/viewing, and notice viewing.",
      image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?crop=entropy&cs=srgb&fm=jpg&ixlib=rb-4.1.0&q=85",
      technologies: ["React.js", "Next.js", "Express", "MongoDB", "Node.js", "WebSocket"],
      category: "Full Stack",
      links: {
        live: "https://unihubfrontend.vercel.app",
        github: "https://github.com/NischalGautam8/unihubfrontend",
        backend: "https://github.com/NischalGautam8/unihubbackend"
      },
      credentials: "Demo: username: nischal, password: nischal",
      featured: true
    },
    {
      title: "The Experience",
      subtitle: "Vacation Booking Platform",
      description: "A modern vacation booking platform with admin dashboard, lazy loading, and seamless user experience for travel enthusiasts.",
      image: "https://images.unsplash.com/photo-1488646953014-85cb44e25828?crop=entropy&cs=srgb&fm=jpg&ixlib=rb-4.1.0&q=85",
      technologies: ["TRPC", "Next.js", "Tailwind CSS", "Mantine UI"],
      category: "Frontend",
      links: {
        live: "https://the-experience-seven.vercel.app/",
        github: "https://github.com/NischalGautam8/the-experience"
      },
      featured: true
    },
    {
      title: "AI Agent",
      subtitle: "Current Affairs Chatbot",
      description: "An intelligent AI agent capable of web searches for current affairs questions with RAG implementation and text streaming.",
      image: "https://images.unsplash.com/photo-1677442136019-21780ecad995?crop=entropy&cs=srgb&fm=jpg&ixlib=rb-4.1.0&q=85",
      technologies: ["Langchain.js", "Langgraph.js", "AI/ML", "RAG"],
      category: "AI/ML",
      links: {
        github: "https://github.com/NischalGautam8/ai_agent"
      },
      featured: true
    },
    {
      title: "Miti",
      subtitle: "Nepali Calendar Application",
      description: "A feature-rich Nepali calendar with language translation, date switching, event creation, and monthly events display.",
      image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?crop=entropy&cs=srgb&fm=jpg&ixlib=rb-4.1.0&q=85",
      technologies: ["React.js", "JavaScript", "CSS", "Git"],
      category: "Frontend",
      links: {
        live: "https://miti.bikram.io",
        github: "https://github.com/PoskOfficial/Miti"
      }
    },
    {
      title: "Fitness Microservice",
      subtitle: "Microservices Architecture Application",
      description: "A fitness application built with microservices architecture, featuring Google Gemini AI integration and RabbitMQ queuing.",
      image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?crop=entropy&cs=srgb&fm=jpg&ixlib=rb-4.1.0&q=85",
      technologies: ["Java", "Spring Boot", "RabbitMQ", "Eureka", "Google Gemini AI"],
      category: "Backend",
      links: {
        github: "https://github.com/NischalGautam8/fitness_microservice/"
      }
    },
    {
      title: "Fitness MERN Stack",
      subtitle: "Full Stack Fitness Application",
      description: "A comprehensive fitness tracking app with AI recommendations, image uploads, and cuss word detection using Hugging Face models.",
      image: "https://images.unsplash.com/photo-1534438327276-14e5300c3a48?crop=entropy&cs=srgb&fm=jpg&ixlib=rb-4.1.0&q=85",
      technologies: ["React.js", "Node.js", "Express", "MongoDB", "Google Gemini AI", "Hugging Face"],
      category: "Full Stack",
      links: {
        live: "https://fit-trackerpclient.vercel.app/",
        github: "https://github.com/NischalGautam8/fit-trackerpclient"
      }
    },
    {
      title: "Netflix Clone",
      subtitle: "Movie Streaming Platform",
      description: "A pixel-perfect Netflix clone using TMDB API with modern React.js and responsive design.",
      image: "https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?crop=entropy&cs=srgb&fm=jpg&ixlib=rb-4.1.0&q=85",
      technologies: ["React.js", "Tailwind CSS", "TMDB API", "Responsive Design"],
      category: "Frontend",
      links: {
        live: "https://netflix-clone-phi-three-56.vercel.app/",
        github: "https://github.com/NischalGautam8/Netflix-Clone"
      }
    },
    {
      title: "URL Shortener",
      subtitle: "RESTful Service in Go",
      description: "A high-performance URL shortening service built with Go, featuring clean architecture and efficient algorithms.",
      image: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?crop=entropy&cs=srgb&fm=jpg&ixlib=rb-4.1.0&q=85",
      technologies: ["Go", "REST API", "Backend"],
      category: "Backend",
      links: {
        github: "https://github.com/NischalGautam8/URL_shortner"
      }
    }
  ];

  const categories = ['All', 'Full Stack', 'Frontend', 'Backend', 'AI/ML'];

  const filteredProjects = filter === 'All' 
    ? projects 
    : projects.filter(project => project.category === filter);

  return (
    <section id="projects" className="section-spacing relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-40 left-10 w-80 h-80 bg-cyan-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-40 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl" />
        <div className="absolute top-20 right-20 w-64 h-64 bg-green-500/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="space-y-20"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center space-y-4">
            <h2 className="text-4xl md:text-5xl font-bold">
              Featured <span className="gradient-text">Projects</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              A showcase of my latest work and innovative solutions
            </p>
          </motion.div>

          {/* Filter Buttons */}
          <motion.div variants={itemVariants} className="flex flex-wrap justify-center gap-6">
            {categories.map((category) => (
              <motion.button
                key={category}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setFilter(category)}
                className={`px-6 py-2 rounded-full font-medium transition-all duration-300 ${
                  filter === category
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                    : 'glass card-padding text-gray-400 hover:text-white hover:bg-blue-500/20'
                }`}
              >
                {category}
              </motion.button>
            ))}
          </motion.div>

          {/* Projects Grid */}
          <motion.div 
            layout
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.title}
                layout
                variants={itemVariants}
                whileHover={{ y: -10, scale: 1.02 }}
                className={`glass-strong rounded-2xl overflow-hidden group hover:border-blue-500/30 transition-all duration-300 ${
                  project.featured ? 'lg:col-span-2' : ''
                }`}
              >
                {/* Project Image */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={project.image}
                    alt={`${project.title} project screenshot`}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-slate-900/80 to-transparent" />
                  
                  {/* Project Links Overlay */}
                  <div className="absolute top-4 right-4 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    {project.links.live && (
                      <motion.a
                        href={project.links.live}
                        target="_blank"
                        rel="noopener noreferrer"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                        className="py-2 card-padding-sm glass rounded-lg text-white hover:bg-blue-500/30 transition-all duration-300"
                        title="Live Demo"
                      >
                        <LaunchIcon fontSize="small" />
                      </motion.a>
                    )}
                    {project.links.github && (
                      <motion.a
                        href={project.links.github}
                        target="_blank"
                        rel="noopener noreferrer"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                        className="py-2 card-padding-sm glass rounded-lg text-white hover:bg-blue-500/30 transition-all duration-300"
                        title="GitHub Repository"
                      >
                        <GitHubIcon fontSize="small" />
                      </motion.a>
                    )}
                  </div>

                  {/* Featured Badge */}
                  {project.featured && (
                    <div className="absolute top-4 left-4">
                      <span className="px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs font-bold rounded-full">
                        FEATURED
                      </span>
                    </div>
                  )}
                </div>

                {/* Project Content */}
                <div className="py-6 card-padding space-y-4">
                  <div className="space-y-2">
                    <h3 className="text-xl font-bold text-white group-hover:text-blue-300 transition-colors">
                      {project.title}
                    </h3>
                    <p className="text-sm text-accent font-medium">{project.subtitle}</p>
                    <p className="text-gray-400 text-sm leading-relaxed">
                      {project.description}
                    </p>
                    {project.credentials && (
                      <p className="text-xs text-yellow-400 font-mono bg-yellow-400/10 p-2 rounded">
                        {project.credentials}
                      </p>
                    )}
                  </div>

                  {/* Technologies */}
                  <div className="space-y-2">
                    <h4 className="text-xs font-semibold text-gray-400 uppercase tracking-wide">
                      Technologies
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.map((tech, techIndex) => (
                        <span
                          key={tech}
                          className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded text-xs font-medium"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Additional Links */}
                  {(project.links.backend || project.links.live) && (
                    <div className="flex gap-2 pt-2">
                      {project.links.backend && (
                        <a
                          href={project.links.backend}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-gray-400 hover:text-white transition-colors flex items-center gap-1"
                        >
                          <CodeIcon fontSize="small" />
                          Backend
                        </a>
                      )}
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Call to Action */}
          <motion.div variants={itemVariants} className="cta-spacing text-center space-y-8">
            <h3 className="text-2xl font-bold text-white">
              Want to See More?
            </h3>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Check out my GitHub profile for more projects and contributions to open source.
            </p>
            <motion.a
              href="https://github.com/NischalGautam8"
              target="_blank"
              rel="noopener noreferrer"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center gap-2 btn-primary"
            >
              <GitHubIcon />
              Visit GitHub
            </motion.a>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Projects;