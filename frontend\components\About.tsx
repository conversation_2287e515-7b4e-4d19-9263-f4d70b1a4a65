'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';

const About = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const stats = [
    { number: "3+", label: "Years Experience" },
    { number: "15+", label: "Projects Completed" },
    { number: "5+", label: "Technologies Mastered" },
    { number: "100%", label: "Client Satisfaction" },
  ];

  const coreSkills = [
    {
      title: "Full-stack Development",
      description: "End-to-end web application development with modern frameworks"
    },
    {
      title: "JavaScript Frameworks",
      description: "React, Vue, Next.js for dynamic user interfaces"
    },
    {
      title: "Backend & APIs",
      description: "Node.js, Express, and RESTful API development"
    },
    {
      title: "Real-time Applications",
      description: "WebSocket integration for live features"
    },
    {
      title: "AI Integration",
      description: "Modern AI tools and machine learning integration"
    }
  ];

  return (
    <section id="about" className="section-spacing relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="space-y-24"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <h2 className="text-4xl md:text-6xl font-bold">
              About <span className="gradient-text">Me</span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto text-spacing">
              Full-stack developer passionate about creating innovative web solutions that make a difference
            </p>
          </motion.div>

          {/* Main Content */}
          <div className="grid lg:grid-cols-5 gap-12 items-start">
            {/* Left Content - Story */}
            <motion.div variants={itemVariants} className="lg:col-span-3 space-y-12">
              {/* Introduction Card */}
              <div className="modern-card content-block-lg">
                <h3 className="text-3xl font-bold text-white mb-8">
                  Building the Future, One Line of Code at a Time
                </h3>
                <p className="text-gray-300 text-lg text-spacing-lg">
                  I'm a Full Stack Developer pursuing my Bachelor's in Software Engineering at Pokhara University. 
                  I specialize in creating scalable, user-friendly applications using modern web technologies 
                  like React.js, Node.js, and Vue.js.
                </p>
                <p className="text-gray-400 text-spacing">
                  My passion lies in transforming complex problems into elegant, efficient solutions that 
                  enhance user experiences and drive business growth.
                </p>
              </div>

              {/* Core Skills */}
              <div className="space-y-8">
                <h4 className="text-2xl font-bold text-white">Core Expertise</h4>
                <div className="grid gap-6">
                  {coreSkills.map((skill, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -30 }}
                      animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -30 }}
                      transition={{ delay: 0.5 + index * 0.1 }}
                      className="modern-card-subtle p-6 hover:scale-[1.02] transition-all duration-300"
                    >
                      <div className="flex items-start gap-4">
                        <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full mt-2 flex-shrink-0" />
                        <div className="space-y-2">
                          <h5 className="text-white font-semibold text-lg">{skill.title}</h5>
                          <p className="text-gray-400 text-sm leading-relaxed">{skill.description}</p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Right Content - Stats & Learning */}
            <motion.div variants={itemVariants} className="lg:col-span-2 space-y-8">
              {/* Stats Card */}
              <div className="modern-card content-block-lg">
                <h4 className="text-2xl font-bold text-white mb-8 text-center">Quick Stats</h4>
                <div className="grid grid-cols-2 gap-8">
                  {stats.map((stat, index) => (
                    <motion.div
                      key={index}
                      initial={{ scale: 0, rotate: -10 }}
                      animate={isInView ? { scale: 1, rotate: 0 } : { scale: 0, rotate: -10 }}
                      transition={{ delay: 0.5 + index * 0.1, type: "spring", stiffness: 100 }}
                      className="text-center space-y-3 p-4 rounded-xl bg-gradient-to-br from-blue-500/10 to-purple-500/10 hover:from-blue-500/20 hover:to-purple-500/20 transition-all duration-300"
                    >
                      <div className="text-4xl font-bold gradient-text">{stat.number}</div>
                      <div className="text-gray-400 text-sm font-medium">{stat.label}</div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Currently Learning */}
              <div className="modern-card content-block">
                <h4 className="text-xl font-bold text-white mb-6">Currently Learning</h4>
                <div className="flex flex-wrap gap-3">
                  {["Go", "Microservices", "AI/ML", "Cloud Computing"].map((tech, index) => (
                    <motion.span
                      key={index}
                      initial={{ opacity: 0, scale: 0, y: 20 }}
                      animate={isInView ? { opacity: 1, scale: 1, y: 0 } : { opacity: 0, scale: 0, y: 20 }}
                      transition={{ delay: 0.8 + index * 0.1, type: "spring" }}
                      whileHover={{ scale: 1.1, y: -2 }}
                      className="px-4 py-2 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 text-blue-300 rounded-full text-sm font-medium border border-blue-500/30 hover:border-blue-400/50 transition-all duration-300 cursor-default"
                    >
                      {tech}
                    </motion.span>
                  ))}
                </div>
              </div>

              {/* Personal Touch */}
              <div className="modern-card-subtle content-block">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 mx-auto bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                    <span className="text-2xl">🚀</span>
                  </div>
                  <h5 className="text-white font-semibold">Always Growing</h5>
                  <p className="text-gray-400 text-sm leading-relaxed">
                    Continuously learning new technologies and improving my craft to deliver better solutions.
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;