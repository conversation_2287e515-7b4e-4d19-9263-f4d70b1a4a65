'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef, useState } from 'react';

const Technologies = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [activeCategory, setActiveCategory] = useState<string | null>(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.8 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const bubbleVariants = {
    hidden: { opacity: 0, scale: 0 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const technologies = {
    "Languages": {
      color: "from-yellow-400 to-orange-500",
      bgColor: "bg-gradient-to-br from-yellow-500/20 to-orange-500/20",
      techs: [
        { name: "JavaScript", color: "from-yellow-400 to-yellow-600", size: "large" },
        { name: "TypeScript", color: "from-blue-400 to-blue-600", size: "large" },
        { name: "C++", color: "from-blue-500 to-blue-700", size: "medium" },
        { name: "Java", color: "from-red-400 to-red-600", size: "medium" },
        { name: "Go", color: "from-cyan-400 to-cyan-600", size: "small" },
        { name: "SQL", color: "from-orange-400 to-orange-600", size: "medium" },
      ]
    },
    "Frontend": {
      color: "from-cyan-400 to-blue-500",
      bgColor: "bg-gradient-to-br from-cyan-500/20 to-blue-500/20",
      techs: [
        { name: "React.js", color: "from-cyan-400 to-cyan-600", size: "large" },
        { name: "Next.js", color: "from-gray-400 to-gray-600", size: "large" },
        { name: "Vue.js", color: "from-green-400 to-green-600", size: "medium" },
        { name: "Tailwind CSS", color: "from-teal-400 to-teal-600", size: "medium" },
        { name: "Framer Motion", color: "from-purple-400 to-purple-600", size: "small" },
      ]
    },
    "Backend": {
      color: "from-green-400 to-emerald-500",
      bgColor: "bg-gradient-to-br from-green-500/20 to-emerald-500/20",
      techs: [
        { name: "Node.js", color: "from-green-400 to-green-600", size: "large" },
        { name: "Express.js", color: "from-gray-400 to-gray-600", size: "medium" },
        { name: "MongoDB", color: "from-green-500 to-green-700", size: "medium" },
        { name: "Langchain", color: "from-blue-400 to-blue-600", size: "small" },
      ]
    },
  };

  const additionalSkills = [
    { name: "Git & GitHub", category: "DevOps" },
    { name: "REST APIs", category: "Backend" },
    { name: "WebSocket", category: "Backend" },
    { name: "Microservices", category: "Architecture" },
    { name: "Docker", category: "DevOps" },
    { name: "Vercel", category: "Cloud" },
    { name: "Netlify", category: "Cloud" },
    { name: "Responsive Design", category: "Frontend" },
    { name: "Performance Optimization", category: "Frontend" },
  ];

  const getBubbleSize = (size: string) => {
    switch (size) {
      case 'large': return 'w-24 h-24 text-sm';
      case 'medium': return 'w-20 h-20 text-xs';
      case 'small': return 'w-16 h-16 text-xs';
      default: return 'w-20 h-20 text-xs';
    }
  };

  const getRandomPosition = (index: number, total: number) => {
    const angle = (index / total) * 2 * Math.PI + Math.random() * 0.5;
    const radius = 120 + Math.random() * 80;
    const x = Math.cos(angle) * radius;
    const y = Math.sin(angle) * radius;
    return {
      left: `calc(50% + ${x}px)`,
      top: `calc(50% + ${y}px)`,
    };
  };

  return (
    <section id="technologies" className="section-spacing relative overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-green-500/5 to-emerald-500/5 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="space-y-20"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <h2 className="text-4xl md:text-6xl font-bold">
              <span className="gradient-text">Tech</span> Arsenal
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              A modern toolkit for building next-generation applications
            </p>
          </motion.div>

          {/* Interactive Category Cards */}
          <motion.div variants={itemVariants} className="grid lg:grid-cols-3 gap-8">
            {Object.entries(technologies).map(([category, data], categoryIndex) => (
              <motion.div
                key={category}
                variants={itemVariants}
                whileHover={{ scale: 1.02, y: -5 }}
                onHoverStart={() => setActiveCategory(category)}
                onHoverEnd={() => setActiveCategory(null)}
                className="tech-category-card py-8 card-padding-lg cursor-pointer group"
              >
                {/* Category Header */}
                <div className="text-center mb-8">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r ${data.color} bg-opacity-20 flex items-center justify-center`}>
                    <div className={`w-8 h-8 rounded-lg bg-gradient-to-r ${data.color}`} />
                  </div>
                  <h3 className="text-2xl font-bold text-white group-hover:text-blue-300 transition-colors">
                    {category}
                  </h3>
                </div>

                {/* Technology Constellation */}
                <div className="tech-constellation relative">
                  {data.techs.map((tech, index) => {
                    const position = getRandomPosition(index, data.techs.length);
                    return (
                      <motion.div
                        key={tech.name}
                        variants={bubbleVariants}
                        initial="hidden"
                        animate={isInView ? "visible" : "hidden"}
                        transition={{ delay: categoryIndex * 0.2 + index * 0.1 }}
                        className={`tech-bubble floating-tech ${getBubbleSize(tech.size)} ${data.bgColor}`}
                        style={position}
                        whileHover={{ 
                          scale: 1.2,
                          zIndex: 20,
                          transition: { duration: 0.2 }
                        }}
                      >
                        <span className="text-white font-semibold text-center leading-tight">
                          {tech.name}
                        </span>
                      </motion.div>
                    );
                  })}
                  
                  {/* Center Glow Effect */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full blur-2xl" />
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Skills Cloud */}
          <motion.div variants={itemVariants} className="subsection-spacing-lg space-y-8">
            <div className="text-center">
              <h3 className="text-3xl font-bold text-white mb-4">
                Additional <span className="gradient-text">Expertise</span>
              </h3>
              <p className="text-gray-400">
                Tools and methodologies that enhance development workflow
              </p>
            </div>

            <div className="glass-strong rounded-3xl py-8 card-padding-lg">
              <div className="skill-cloud">
                {additionalSkills.map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, scale: 0, rotate: -10 }}
                    animate={isInView ? { 
                      opacity: 1, 
                      scale: 1, 
                      rotate: 0,
                      transition: { 
                        delay: 1.5 + index * 0.05,
                        duration: 0.5,
                        ease: "easeOut"
                      }
                    } : {}}
                    whileHover={{ 
                      scale: 1.1, 
                      y: -3,
                      transition: { duration: 0.2 }
                    }}
                    className={`skill-tag glass card-padding-sm text-gray-300 hover:text-white hover:bg-blue-500/20 ${
                      skill.category === 'DevOps' ? 'bg-orange-500/20 text-orange-300' :
                      skill.category === 'Backend' ? 'bg-green-500/20 text-green-300' :
                      skill.category === 'Frontend' ? 'bg-cyan-500/20 text-cyan-300' :
                      skill.category === 'Cloud' ? 'bg-purple-500/20 text-purple-300' :
                      skill.category === 'Architecture' ? 'bg-red-500/20 text-red-300' :
                      skill.category === 'Quality' ? 'bg-yellow-500/20 text-yellow-300' :
                      'bg-blue-500/20 text-blue-300'
                    }`}
                  >
                    {skill.name}
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Call to Action */}
          <motion.div variants={itemVariants} className="cta-spacing text-center space-y-8">
            <h3 className="text-2xl font-bold text-white">
              Ready to Build Something Amazing?
            </h3>
            <p className="text-gray-400  mx-auto">
              Let's combine these technologies to create innovative solutions that make a difference.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary"
              onClick={() => document.querySelector('#contact')?.scrollIntoView({ behavior: 'smooth' })}
            >
              Start a Project
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Technologies;