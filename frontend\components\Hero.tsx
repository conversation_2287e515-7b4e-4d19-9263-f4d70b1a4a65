'use client';

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import GitHubIcon from '@mui/icons-material/GitHub';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import CodeIcon from '@mui/icons-material/Code';

const Hero = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
  };

  return (
    <section id="home" className="min-h-screen hero-section relative overflow-hidden flex items-center">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div 
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1524168644224-a521b6533306?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw2fHx0ZWNobm9sb2d5JTIwYWJzdHJhY3QlMjBnZW9tZXRyaWMlMjBjb2RpbmclMjBkYXJrfGVufDB8MHx8Ymx1ZXwxNzU0NTY1NTUxfDA&ixlib=rb-4.1.0&q=85')`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
        
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900/90 via-slate-800/80 to-slate-900/90" />
        
        {/* Animated Particles */}
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-blue-400 rounded-full opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
        
        {/* Mouse follower gradient */}
        <motion.div
          className="absolute w-96 h-96 rounded-full opacity-10"
          style={{
            background: 'radial-gradient(circle, rgba(59,130,246,0.3) 0%, transparent 70%)',
            left: mousePosition.x - 192,
            top: mousePosition.y - 192,
          }}
          animate={{
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
          }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid lg:grid-cols-2 gap-12 items-center"
        >
          {/* Left Content */}
          <div className="space-y-8">
            <motion.div variants={itemVariants} className="space-y-4">
              <motion.h1 
                className="text-5xl md:text-7xl font-bold leading-tight"
                variants={itemVariants}
              >
                <span className="gradient-text">Nischal</span>
                <br />
                <span className="text-white">Gautam</span>
              </motion.h1>
              
              <motion.div
                variants={itemVariants}
                className="text-xl md:text-2xl text-gray-300 font-light"
              >
                <span className="font-mono text-accent">{'<'}</span>
                Full Stack Developer
                <span className="font-mono text-accent">{' />'}</span>
              </motion.div>
            </motion.div>

            <motion.p
              variants={itemVariants}
              className="text-lg text-gray-400 max-w-lg leading-relaxed"
            >
              Building innovative solutions with modern technologies. 
              Specializing in React, Node.js, and scalable applications.
            </motion.p>

            <motion.div variants={itemVariants} className="flex flex-wrap gap-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="btn-primary"
                onClick={() => document.querySelector('#projects')?.scrollIntoView({ behavior: 'smooth' })}
              >
                View My Work
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="btn-outline"
                onClick={() => document.querySelector('#contact')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Get In Touch
              </motion.button>
            </motion.div>

            {/* Contact Info */}
            <motion.div variants={itemVariants} className="space-y-3">
              <div className="flex items-center gap-3 text-gray-400">
                <LocationOnIcon className="text-accent" />
                <span>Kathmandu, Nepal</span>
              </div>
              <div className="flex items-center gap-3 text-gray-400">
                <EmailIcon className="text-accent" />
                <a href="mailto:<EMAIL>" className="hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center gap-3 text-gray-400">
                <PhoneIcon className="text-accent" />
                <span>+977-9821597160</span>
              </div>
            </motion.div>

            {/* Social Links */}
            <motion.div variants={itemVariants} className="flex gap-4">
              {[
                { icon: GitHubIcon, href: 'https://github.com/NischalGautam8', label: 'GitHub' },
                { icon: LinkedInIcon, href: 'https://linkedin.com/in/nischalgautam8', label: 'LinkedIn' },
                { icon: CodeIcon, href: 'https://leetcode.com/u/nischalgautam7200/', label: 'LeetCode' },
              ].map(({ icon: Icon, href, label }) => (
                <motion.a
                  key={label}
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="py-3 card-padding-sm glass rounded-lg text-gray-400 hover:text-white hover:bg-blue-500/20 transition-all duration-300"
                  aria-label={label}
                >
                  <Icon />
                </motion.a>
              ))}
            </motion.div>
          </div>

          {/* Right Content - Floating Code Block */}
          <motion.div
            variants={floatingVariants}
            animate="animate"
            className="relative"
          >
            <motion.div
              variants={itemVariants}
              className="glass-strong rounded-2xl py-8 card-padding-lg border border-blue-500/20"
            >
              <div className="flex items-center gap-2 mb-4">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="ml-4 text-gray-400 text-sm font-mono">developer.js</span>
              </div>
              
              <div className="font-mono text-sm space-y-2">
                <div className="text-purple-400">const <span className="text-blue-400">developer</span> = {`{`}</div>
                <div className="ml-4 text-gray-300">name: <span className="text-green-400">'Nischal Gautam'</span>,</div>
                <div className="ml-4 text-gray-300">role: <span className="text-green-400">'Full Stack Developer'</span>,</div>
                <div className="ml-4 text-gray-300">location: <span className="text-green-400">'Kathmandu, Nepal'</span>,</div>
                <div className="ml-4 text-gray-300">skills: [</div>
                <div className="ml-8 text-green-400">'React', 'Node.js', 'TypeScript',</div>
                <div className="ml-8 text-green-400">'MongoDB', 'Express', 'Next.js'</div>
                <div className="ml-4 text-gray-300">],</div>
                <div className="ml-4 text-gray-300">passion: <span className="text-green-400">'Building amazing things'</span></div>
                <div className="text-purple-400">{`};`}</div>
              </div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 2 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center"
        >
          <motion.div
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-1 h-3 bg-gray-400 rounded-full mt-2"
          />
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Hero;